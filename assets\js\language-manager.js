// Language Manager - A more robust approach to handling language changes

// Cache for language modules
const languageCache = {};

// Current language
let currentLanguage = "en";

// Function to load a language module
async function loadLanguageModule(lang) {
  // Check if we already have this language in cache
  if (languageCache[lang]) {
    return languageCache[lang];
  }

  try {
    // Import the language module
    const module = await import(`./lang/${lang}.js`);

    // Cache the module
    languageCache[lang] = module.default;

    return module.default;
  } catch (error) {
    // If loading fails and it's not English, try to fall back to English
    if (lang !== "en") {
      return loadLanguageModule("en");
    }

    // If English fails, return a minimal translation object
    return {
      dir: "ltr",
      settings: {
        title: "Settings",
        save: "Save",
        close: "Close",
        language: "Language",
      },
      stats: {
        title: "Statistics",
        best: "Best",
        worst: "Worst",
        mean: "Mean",
        avg5: "ao5",
        avg12: "ao12",
        avg100: "ao100",
        solves: "Solves",
      },
    };
  }
}

// Function to change the language
async function changeLanguage(lang) {
  try {
    // Save language preference
    localStorage.setItem("scTimer-language", lang);

    // Load the language module
    const translations = await loadLanguageModule(lang);

    // Update the current language
    currentLanguage = lang;

    // Set document direction
    const direction = translations.dir || "ltr";
    document.documentElement.setAttribute("dir", direction);

    // Make translations available globally
    window.i18nModule = { translations, currentLanguage };
    window.currentLanguage = currentLanguage;

    // Apply translations to the page
    applyTranslations(translations);

    // Dispatch custom event for tutorial manager and other components
    const languageChangeEvent = new CustomEvent("languageChanged", {
      detail: { language: lang, translations: translations },
    });
    document.dispatchEvent(languageChangeEvent);

    // Update tutorial manager if it exists
    if (
      window.tutorialManager &&
      typeof window.tutorialManager.updateTutorialContent === "function"
    ) {
      window.tutorialManager.currentLanguage = lang;
      window.tutorialManager.updateTutorialContent();
    }

    return true;
  } catch (error) {
    return false;
  }
}

// Function to apply translations to the page
function applyTranslations(translations) {
  try {
    // Apply translations to all elements with data-i18n attribute
    const elementsWithI18n = document.querySelectorAll("[data-i18n]");
    elementsWithI18n.forEach((element) => {
      const key = element.getAttribute("data-i18n");
      const translation = getNestedTranslation(translations, key);
      if (translation) {
        element.textContent = translation;
      }
    });

    // Apply translations to all elements with data-i18n-placeholder attribute
    const elementsWithPlaceholder = document.querySelectorAll(
      "[data-i18n-placeholder]"
    );
    elementsWithPlaceholder.forEach((element) => {
      const key = element.getAttribute("data-i18n-placeholder");
      const translation = getNestedTranslation(translations, key);
      if (translation) {
        element.setAttribute("placeholder", translation);
      }
    });

    // Update settings modal
    updateElement(".settings-title", translations.settings.title);
    updateElement(
      "#settings-save",
      `<i class="fas fa-check"></i> ${translations.settings.save}`
    );
    updateElementAttribute(
      "#settings-close",
      "title",
      translations.settings.close
    );

    // Update settings sections
    const sectionTitles = document.querySelectorAll(".settings-section-title");
    if (sectionTitles.length >= 4) {
      sectionTitles[0].textContent = translations.timerOptions.title;
      sectionTitles[1].textContent = translations.displayOptions.title;
      sectionTitles[2].textContent = translations.settings.language;
      sectionTitles[3].textContent = translations.sync.title;
    }

    // Update checkbox labels
    updateCheckboxLabels(translations);

    // Update times panel
    updateElement(".times-panel-title", translations.times.title);
    updateElement("#clear-times", translations.times.clear);
    updateElementAttribute(
      "#times-panel-close",
      "title",
      translations.times.close
    );

    // Update solve details modal
    updateElement(".solve-details-title", translations.solveDetails.title);
    updateElementAttribute(
      "#solve-details-close",
      "title",
      translations.solveDetails.close
    );
    updateElement(
      "#solve-details-save",
      `<i class="fas fa-check"></i> ${translations.solveDetails.save}`
    );

    // Update solve details sections
    const detailsSectionTitles = document.querySelectorAll(
      ".solve-details-section-title"
    );
    if (detailsSectionTitles.length >= 3) {
      detailsSectionTitles[0].textContent = translations.solveDetails.scramble;
      detailsSectionTitles[1].textContent = translations.solveDetails.penalty;
      detailsSectionTitles[2].textContent = translations.solveDetails.comment;
    }

    // Update penalty options
    const penaltyLabels = document.querySelectorAll(
      ".solve-details-penalty label"
    );
    if (penaltyLabels.length > 0 && penaltyLabels[0].childNodes.length > 1) {
      penaltyLabels[0].childNodes[1].textContent =
        translations.solveDetails.none;
    }

    // Update comment placeholder
    updateElementAttribute(
      "#solve-details-comment",
      "placeholder",
      translations.solveDetails.addComment
    );

    // Update buttons
    updateElement("#new-scramble", translations.buttons.next);
    updateElementAttribute(
      "#times-toggle",
      "title",
      translations.buttons.viewTimes
    );

    // Update statistics panel
    updateElement(".stats-title", translations.stats.title);

    // Update statistics labels
    updateStatLabels(translations);

    // Update statistics details labels
    updateStatsDetailsLabels(translations);

    // Update event dropdown options
    updateEventOptions(translations);

    // Update current event text
    updateCurrentEvent(translations);

    // Update timer state text
    updateTimerState(translations);

    // Update debug info
    updateDebugInfo(translations);

    // Update delete button titles
    updateDeleteButtons(translations);

    // Update loading scramble text
    updateLoadingScramble(translations);

    // Update radio button labels
    updateRadioLabels(translations);

    // Update clear times button
    updateClearTimesButton(translations);

    // Update scramble font size text
    updateScrambleFontSizeText(translations);

    // Update times toggle button position
    if (window.updateTimesTogglePosition) {
      window.updateTimesTogglePosition();
    }

    // Force update of statistics panel
    if (window.updateStatistics) {
      window.updateStatistics();
    }
  } catch (error) {
    // Silent error handling
  }
}

// Helper function to update an element's text content
function updateElement(selector, text) {
  const element = document.querySelector(selector);
  if (element) {
    if (text.includes("<i class=")) {
      element.innerHTML = text;
    } else {
      element.textContent = text;
    }
  }
}

// Helper function to update an element's attribute
function updateElementAttribute(selector, attribute, value) {
  const element = document.querySelector(selector);
  if (element) {
    element.setAttribute(attribute, value);
  }
}

// Function to update checkbox labels
function updateCheckboxLabels(translations) {
  // Update checkbox labels
  const checkboxLabels = document.querySelectorAll(".checkbox-label");
  if (checkboxLabels.length >= 5) {
    checkboxLabels.forEach((label) => {
      const input = label.querySelector("input");
      if (input) {
        const id = input.id;

        // Remove the input temporarily
        if (label.contains(input)) {
          label.removeChild(input);

          // Clear the label's contents
          label.textContent = "";

          // Add the input back
          label.appendChild(input);

          // Add the translated text
          if (id === "use-inspection") {
            label.appendChild(
              document.createTextNode(translations.timerOptions.useInspection)
            );
          } else if (id === "use-inspection-sounds") {
            label.appendChild(
              document.createTextNode(
                translations.timerOptions.useInspectionSounds
              )
            );
          } else if (id === "show-visualization") {
            label.appendChild(
              document.createTextNode(
                translations.displayOptions.showVisualization
              )
            );
          } else if (id === "show-stats") {
            label.appendChild(
              document.createTextNode(translations.displayOptions.showStats)
            );
          } else if (id === "show-debug") {
            label.appendChild(
              document.createTextNode(translations.displayOptions.showDebug)
            );
          } else if (id === "dark-mode") {
            label.appendChild(
              document.createTextNode(translations.displayOptions.darkMode)
            );
          } else if (id === "show-fmc-keyboard") {
            label.appendChild(
              document.createTextNode(
                translations.displayOptions.showFMCKeyboard
              )
            );
          } else if (id === "stackmat-reset-inspection") {
            label.appendChild(
              document.createTextNode(
                translations.timerOptions.stackmatResetInspection
              )
            );
          }
        }
      }
    });
  }

  // Update dropdown labels
  const dropdownLabels = document.querySelectorAll(".dropdown-label");
  dropdownLabels.forEach((label) => {
    const span = label.querySelector("span");
    const select = label.querySelector("select");

    if (span && select) {
      const id = select.id;

      // Update the label text
      if (id === "timer-mode-selector") {
        span.textContent = translations.timerOptions.timerMode;

        // Update the option texts
        const options = select.querySelectorAll("option");
        options.forEach((option) => {
          if (option.value === "timer") {
            option.textContent = translations.timerOptions.timerModeTimer;
          } else if (option.value === "typing") {
            option.textContent = translations.timerOptions.timerModeTyping;
          } else if (option.value === "stackmat") {
            option.textContent = translations.timerOptions.timerModeStackmat;
          }
        });
      }
    }
  });
}

// Function to update statistics labels
function updateStatLabels(translations) {
  const statItems = document.querySelectorAll(".stat-item");
  if (statItems.length >= 6) {
    statItems.forEach((item, index) => {
      const label = item.querySelector(".stat-label");
      if (label) {
        switch (index) {
          case 0: // Solves
            label.textContent = translations.stats.solves + ":";
            break;
          case 1: // Best
            label.textContent = translations.stats.best + ":";
            break;
          case 2: // Mean
            label.textContent = translations.stats.mean + ":";
            break;
          case 3: // ao5
            label.textContent = translations.stats.avg5 + ":";
            break;
          case 4: // ao12
            label.textContent = translations.stats.avg12 + ":";
            break;
          case 5: // ao100
            label.textContent = translations.stats.avg100 + ":";
            break;
        }
      }
    });
  }
}

// Function to update statistics details labels
function updateStatsDetailsLabels(translations) {
  if (!translations.statsDetails) return;

  // Update statistics details title
  updateElement(".stats-details-title", translations.statsDetails.title);
  updateElement(
    ".stats-details-title-text",
    translations.statsDetails.titleFor
  );

  // Update section titles
  const sectionTitles = document.querySelectorAll(".stats-section-title");
  if (sectionTitles.length >= 6) {
    sectionTitles[0].textContent = translations.statsDetails.overview;
    sectionTitles[1].textContent = translations.statsDetails.averages;
    sectionTitles[2].textContent = translations.statsDetails.records;
    sectionTitles[3].textContent = translations.statsDetails.timeDistribution;
    sectionTitles[4].textContent = translations.statsDetails.progressChart;
    sectionTitles[5].textContent = translations.statsDetails.sessionAnalysis;
    if (sectionTitles[6])
      sectionTitles[6].textContent = translations.statsDetails.predictions;
  }

  // Update record labels
  updateElement(
    "[data-i18n='statsDetails.bestSingle']",
    translations.statsDetails.bestSingle
  );
  updateElement(
    "[data-i18n='statsDetails.bestAo5']",
    translations.statsDetails.bestAo5
  );
  updateElement(
    "[data-i18n='statsDetails.bestAo12']",
    translations.statsDetails.bestAo12
  );
  updateElement(
    "[data-i18n='statsDetails.bestAo100']",
    translations.statsDetails.bestAo100
  );

  // Update analysis labels
  updateElement(
    "[data-i18n='statsDetails.totalTime']",
    translations.statsDetails.totalTime
  );
  updateElement(
    "[data-i18n='statsDetails.averageTime']",
    translations.statsDetails.averageTime
  );
  updateElement(
    "[data-i18n='statsDetails.solvesPerHour']",
    translations.statsDetails.solvesPerHour
  );
  updateElement(
    "[data-i18n='statsDetails.consistency']",
    translations.statsDetails.consistency
  );

  // Update prediction labels
  updateElement(
    "[data-i18n='statsDetails.nextAo5']",
    translations.statsDetails.nextAo5
  );
  updateElement(
    "[data-i18n='statsDetails.nextAo12']",
    translations.statsDetails.nextAo12
  );
  updateElement(
    "[data-i18n='statsDetails.improvementRate']",
    translations.statsDetails.improvementRate
  );
  updateElement(
    "[data-i18n='statsDetails.targetTime']",
    translations.statsDetails.targetTime
  );

  // Update standard deviation label
  updateElement(
    "[data-i18n='statsDetails.standardDeviation']",
    translations.statsDetails.standardDeviation
  );

  // Update session selector options
  updateElement(
    "[data-i18n='statsDetails.currentSession']",
    translations.statsDetails.currentSession
  );
  updateElement(
    "[data-i18n='statsDetails.allSessions']",
    translations.statsDetails.allSessions
  );
}

// Function to update event dropdown options
function updateEventOptions(translations) {
  // First, update all event options with data-i18n attributes
  const eventSpans = document.querySelectorAll(
    ".event-option span[data-i18n^='events.']"
  );
  eventSpans.forEach((span) => {
    const key = span.getAttribute("data-i18n");
    const translation = getNestedTranslation(translations, key);
    if (translation) {
      span.textContent = translation;
    }
  });

  // Then handle any remaining event options without data-i18n attributes
  const eventOptions = document.querySelectorAll(
    ".event-option:not(:has(span[data-i18n^='events.']))"
  );
  eventOptions.forEach((element) => {
    const eventId = element.getAttribute("data-event");
    if (eventId && translations.events && translations.events[eventId]) {
      const icon = element.querySelector(".cubing-icon");
      if (icon) {
        element.innerHTML = "";
        element.appendChild(icon);
        element.appendChild(
          document.createTextNode(" " + translations.events[eventId])
        );
      } else {
        element.textContent = translations.events[eventId];
      }
    }
  });
}

// Function to update current event text
function updateCurrentEvent(translations) {
  const currentEventText = document.getElementById("current-event-text");
  const currentEvent = document.getElementById("current-event");
  if (currentEventText && currentEvent) {
    const eventId = currentEvent.textContent;
    if (eventId && translations.events && translations.events[eventId]) {
      // Update the data-i18n attribute to match the current event
      currentEventText.setAttribute("data-i18n", `events.${eventId}`);
      currentEventText.textContent = translations.events[eventId];
    }
  }
}

// Function to update timer state text
function updateTimerState(translations) {
  const timerState = document.getElementById("timer-state");
  if (timerState) {
    const state = timerState.textContent;
    if (state && translations.timer && translations.timer[state]) {
      timerState.textContent = translations.timer[state];
    }
  }
}

// Function to update debug info
function updateDebugInfo(translations) {
  const debugInfoDivs = document.querySelectorAll("#debug-info div");
  if (debugInfoDivs.length >= 4) {
    // Timer State label
    const timerStateLabel = debugInfoDivs[0];
    if (
      timerStateLabel &&
      timerStateLabel.textContent.includes("Timer State")
    ) {
      const stateSpan = timerStateLabel.querySelector("span");
      if (stateSpan) {
        const spanContent = stateSpan.textContent;
        timerStateLabel.textContent =
          translations.debug?.timerState || "Timer State: ";
        timerStateLabel.appendChild(stateSpan);
        stateSpan.textContent = spanContent;
      }
    }

    // Space Held For label
    const spaceHeldLabel = debugInfoDivs[1];
    if (
      spaceHeldLabel &&
      spaceHeldLabel.textContent.includes("Space Held For")
    ) {
      const timeSpan = spaceHeldLabel.querySelector("span");
      if (timeSpan) {
        const spanContent = timeSpan.textContent;
        spaceHeldLabel.textContent =
          translations.debug?.spaceHeldFor || "Space Held For: ";
        spaceHeldLabel.appendChild(timeSpan);
        timeSpan.textContent = spanContent;
        spaceHeldLabel.appendChild(document.createTextNode("ms"));
      }
    }

    // Current Event label
    const currentEventLabel = debugInfoDivs[2];
    if (
      currentEventLabel &&
      currentEventLabel.textContent.includes("Current Event")
    ) {
      const eventSpan = currentEventLabel.querySelector("span");
      if (eventSpan) {
        const spanContent = eventSpan.textContent;
        currentEventLabel.textContent =
          translations.debug?.currentEvent || "Current Event: ";
        currentEventLabel.appendChild(eventSpan);
        eventSpan.textContent = spanContent;
      }
    }

    // Scramble Source label
    const scrambleSourceLabel = debugInfoDivs[3];
    if (
      scrambleSourceLabel &&
      scrambleSourceLabel.textContent.includes("Scramble Source")
    ) {
      const sourceSpan = scrambleSourceLabel.querySelector("span");
      if (sourceSpan) {
        const spanContent = sourceSpan.textContent;
        scrambleSourceLabel.textContent =
          translations.debug?.scrambleSource || "Scramble Source: ";
        scrambleSourceLabel.appendChild(sourceSpan);
        sourceSpan.textContent = spanContent;
      }
    }
  }
}

// Function to update delete button titles
function updateDeleteButtons(translations) {
  const deleteButtons = document.querySelectorAll(".delete-time");
  deleteButtons.forEach((button) => {
    button.setAttribute("title", translations.times.delete || "Delete time");
  });
}

// Function to update loading scramble text
function updateLoadingScramble(translations) {
  const scramble = document.getElementById("scramble");
  if (scramble && scramble.textContent === "Loading scramble...") {
    scramble.textContent =
      translations.scramble?.loading || "Loading scramble...";
  }
}

// Function to update radio button labels
function updateRadioLabels(translations) {
  const radioLabels = document.querySelectorAll(".radio-label");
  if (radioLabels.length >= 3) {
    // The first one (None) is already handled
    // Handle +2 and DNF labels
    const plusTwoLabel = radioLabels[1];
    const dnfLabel = radioLabels[2];

    if (
      plusTwoLabel.childNodes.length > 1 &&
      translations.solveDetails.plusTwo
    ) {
      plusTwoLabel.childNodes[1].textContent =
        translations.solveDetails.plusTwo;
    }

    if (dnfLabel.childNodes.length > 1 && translations.solveDetails.dnf) {
      dnfLabel.childNodes[1].textContent = translations.solveDetails.dnf;
    }
  }
}

// Function to update clear times button
function updateClearTimesButton(translations) {
  const clearTimesText = document.querySelector(".clear-times-text");
  if (clearTimesText) {
    clearTimesText.textContent = translations.times?.clear || "Clear Times";
  }
}

// Function to update scramble font size text
function updateScrambleFontSizeText(translations) {
  const scrambleFontSizeText = document.querySelector(
    ".scramble-font-size-text"
  );
  if (scrambleFontSizeText) {
    scrambleFontSizeText.textContent =
      translations.displayOptions?.scrambleFontSize || "Scramble Font Size";
  }
}

// Function to get the current language
function getCurrentLanguage() {
  return currentLanguage;
}

// Function to get the current translations
function getTranslations() {
  return languageCache[currentLanguage] || null;
}

// Helper function to get nested translation by key path (e.g., "mbld.clickToView")
function getNestedTranslation(translations, keyPath) {
  if (!keyPath || !translations) return null;

  const keys = keyPath.split(".");
  let result = translations;

  for (const key of keys) {
    if (result && typeof result === "object" && key in result) {
      result = result[key];
    } else {
      return null;
    }
  }

  return result;
}

// Initialize the language manager
async function initLanguageManager() {
  // Try to load from localStorage first
  const savedLang = localStorage.getItem("scTimer-language");

  // Set up the language selector
  const langSelector = document.getElementById("language-selector");
  if (langSelector) {
    langSelector.addEventListener("change", async (e) => {
      await changeLanguage(e.target.value);
    });

    // Set the language selector to the saved language
    if (savedLang) {
      langSelector.value = savedLang;
    }
  }

  // Load the language
  if (savedLang) {
    await changeLanguage(savedLang);
  } else {
    // Try to detect browser language
    const browserLang = navigator.language.split("-")[0];

    // Check if we support this language
    if (["en", "ar", "ckb"].includes(browserLang)) {
      await changeLanguage(browserLang);

      // Update the language selector
      if (langSelector) {
        langSelector.value = browserLang;
      }
    } else {
      await changeLanguage("en");
    }
  }
}

// Make functions available globally for tutorial manager
window.changeLanguage = changeLanguage;
window.getCurrentLanguage = getCurrentLanguage;
window.getTranslations = getTranslations;

// Export the functions
export {
  changeLanguage,
  getCurrentLanguage,
  getTranslations,
  initLanguageManager,
};
