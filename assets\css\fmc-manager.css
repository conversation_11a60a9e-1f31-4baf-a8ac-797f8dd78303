/* FMC Manager Styles */

/* FMC Start Prompt */
.fmc-start-prompt {
  font-size: 1rem;
  color: var(--text-color);
  opacity: 0.8;
  text-align: center;
}

/* FMC Timer Running */
.fmc-timer-running {
  color: var(--timer-color, #2196f3) !important;
  font-size: 2rem !important;
  text-align: center;
  font-weight: bold;
  font-family: "Roboto Mono", monospace;
}

/* FMC Loading State */
.fmc-loading {
  color: var(--accent-color, #a57865) !important;
  font-size: 1.2rem !important;
  text-align: center;
  display: flex !important;
  align-items: center;
  justify-content: center;
}

/* FMC Loading Container */
.fmc-loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

/* FMC Loading Spinner */
.fmc-loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--bg-color-secondary, #f0f0f0);
  border-top: 3px solid var(--accent-color, #a57865);
  border-radius: 50%;
  animation: fmc-spin 1s linear infinite;
}

/* FMC Loading Text */
.fmc-loading-text {
  font-size: 1.2rem;
  font-weight: 500;
  color: var(--text-color, #333);
}

/* FMC Loading Subtext */
.fmc-loading-subtext {
  font-size: 0.9rem;
  color: var(--text-color-secondary, #666);
  opacity: 0.8;
}

/* RTL font support for FMC loading text */
[dir="rtl"] .fmc-loading-text,
[dir="rtl"] .fmc-loading-subtext {
  font-family: var(--font-rtl) !important;
}

/* Spinner Animation */
@keyframes fmc-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* FMC Error State */
.fmc-error {
  color: #e74c3c !important;
  font-size: 1.2rem !important;
  text-align: center;
}

/* FMC Solution Container */
.fmc-solution-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  margin-bottom: 10px;
}

/* FMC Solution Input */
.fmc-solution-input {
  width: 100%;
  min-height: 120px;
  padding: 10px;
  font-family: monospace;
  font-size: 1rem;
  border: 1px solid var(--border-color, #ddd);
  border-radius: 4px;
  background-color: var(--bg-color, #fff);
  color: var(--text-color, #333);
  resize: vertical;
  box-sizing: border-box;
  /* Always keep FMC solution input LTR since cube notation is in English */
  direction: ltr !important;
  text-align: left !important;
}

/* FMC Move Count Display */
.fmc-move-count-display {
  position: absolute;
  bottom: 5px;
  right: 10px;
  font-size: 0.9rem;
  color: var(--text-color-secondary, #666);
  background-color: rgba(255, 255, 255, 0.7);
  padding: 2px 5px;
  border-radius: 3px;
}

/* FMC Timer Display */
.fmc-timer-display {
  font-size: 1.5rem;
  font-weight: bold;
  text-align: center;
  margin: 10px 0;
  color: var(--text-color, #333);
}

/* FMC Validation Message */
.fmc-validation-message {
  margin-top: 10px;
  padding: 8px;
  border-radius: 4px;
  text-align: center;
  font-weight: bold;
}

.fmc-validation-message.valid {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.fmc-validation-message.invalid {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* FMC Keyboard Container - Normal/Light Mode */
.fmc-keyboard-container {
  width: 100%;
  margin-top: 10px;
  background-color: #d0d3da;
  border-radius: 8px;
  padding: 10px;
  box-sizing: border-box;
  /* Always keep FMC keyboard LTR since cube notation is in English */
  direction: ltr !important;
}

/* Dark mode keyboard container */
[data-theme="dark"] .fmc-keyboard-container,
.dark-mode .fmc-keyboard-container {
  background-color: #2b2b2b;
}

/* FMC Keyboard Color Variables */
:root {
  /* Light mode colors - more subtle, modern palette */
  --submit-btn-color: #66bb6a;
  --submit-btn-text: #ffffff;
  --case-btn-color: #9575cd;
  --case-btn-text: #ffffff;
  --backspace-btn-color: #ef9a9a;
  --backspace-btn-text: #424242;
  --space-btn-color: #90a4ae;
  --space-btn-text: #ffffff;
  --viz-btn-color: #7986cb;
  --viz-btn-text: #ffffff;
  --keyboard-btn-color: #78909c;
  --keyboard-btn-text: #ffffff;
  --key-bg-color: #f5f5f5;
  --key-text-color: #424242;
  --cell-bg-color: #f5f5f5;
  --cell-text-color: #424242;
}

/* Dark mode colors - deeper, less harsh colors */
.dark-mode {
  --submit-btn-color: #4caf50;
  --submit-btn-text: #ffffff;
  --case-btn-color: #7e57c2;
  --case-btn-text: #ffffff;
  --backspace-btn-color: #e57373;
  --backspace-btn-text: #ffffff;
  --space-btn-color: #607d8b;
  --space-btn-text: #ffffff;
  --viz-btn-color: #5c6bc0;
  --viz-btn-text: #ffffff;
  --keyboard-btn-color: #546e7a;
  --keyboard-btn-text: #ffffff;
  --key-bg-color: #37474f;
  --key-text-color: #eceff1;
  --cell-bg-color: #37474f;
  --cell-text-color: #eceff1;
}

/* FMC Keyboard */
.fmc-keyboard {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  grid-template-rows: repeat(3, 45px) auto;
  gap: 5px;
  width: 100%;
}

/* Ensure consistent button sizes for top 3 rows */
.fmc-keyboard > *:nth-child(-n + 18) {
  height: 45px;
  min-height: 45px;
  max-height: 45px;
}

/* Bottom row special styling */
.fmc-keyboard > *:nth-child(n + 19) {
  grid-row: 4;
  min-height: 40px;
}

/* Keyboard in hidden state */
.fmc-keyboard.rows-hidden {
  grid-template-rows: 0px 0px 0px auto;
}

.fmc-keyboard.rows-hidden > *:nth-child(-n + 18) {
  height: 0;
  min-height: 0;
  max-height: 0;
  overflow: hidden;
  padding: 0;
  margin: 0;
  border: 0;
}

/* Regular key styling */
.fmc-key {
  background-color: var(--key-bg-color, #f5f5f5) !important;
  color: var(--key-text-color, #333) !important;
  transition: background-color 0.2s, color 0.2s;
}

/* Special buttons override - default styling (normal/light mode) */
.fmc-key[data-special="true"],
.fmc-keyboard-toggle {
  background-color: #a9afbd !important;
  color: #333 !important;
  border-color: #a9afbd !important;
}

.fmc-key[data-special="true"]:hover,
.fmc-keyboard-toggle:hover {
  background-color: #9399a7 !important;
}

/* FMC Keyboard Keys - Normal/Light Mode */
.fmc-key {
  padding: 12px 8px;
  background-color: #ffffff;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  cursor: pointer;
  color: #333;
  text-align: center;
  transition: background-color 0.2s, transform 0.1s;
}

.fmc-key:hover {
  background-color: #f0f0f0;
}

.fmc-key:active {
  transform: scale(0.95);
}

/* FMC Keyboard Timer Cell - Normal/Light Mode (like normal buttons) */
.fmc-keyboard-timer-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px;
}

/* FMC Keyboard Timer */
.fmc-keyboard-timer {
  font-size: 1.2rem;
  font-weight: bold;
  color: #333;
}

/* FMC Keyboard Move Count Cell - Normal/Light Mode (like normal buttons) */
.fmc-keyboard-move-count-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px;
}

/* FMC Keyboard Move Count */
.fmc-keyboard-move-count {
  font-size: 1rem;
  font-weight: bold;
  color: #333;
}

/* FMC Keyboard Timer-Moves Combined Cell - Normal/Light Mode (like normal buttons) */
.fmc-keyboard-timer-moves-cell {
  background-color: #ffffff;
  border: 1px solid #ddd;
  color: #333;
}

/* Dark mode timer-moves combined cell (like normal buttons) */
[data-theme="dark"] .fmc-keyboard-timer-moves-cell,
.dark-mode .fmc-keyboard-timer-moves-cell {
  background-color: #6b6b6b !important;
  border-color: #555 !important;
  color: #fff !important;
}

/* FMC Visualization Modal */
.fmc-visualization-modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 3500;
  justify-content: center;
  align-items: center;
}

.fmc-visualization-modal.show {
  display: flex;
}

/* FMC Visualization Modal Content */
.fmc-visualization-modal-content {
  background-color: var(--bg-color, #fff);
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 90%;
  overflow: auto;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* FMC Visualization Modal Header */
.fmc-visualization-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid var(--border-color, #ddd);
}

/* FMC Visualization Modal Title */
.fmc-visualization-modal-title {
  font-size: 1.2rem;
  font-weight: bold;
  color: var(--text-color, #333);
}

/* FMC Visualization Modal Close Button */
.fmc-visualization-modal-close {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  color: var(--text-color-secondary, #666);
}

/* FMC Visualization Container */
.fmc-visualization-container {
  padding: 15px;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* FMC Result Modal */
.fmc-result-modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 3500;
  justify-content: center;
  align-items: center;
}

.fmc-result-modal.show {
  display: flex;
}

/* FMC Result Modal Content */
.fmc-result-modal-content {
  background-color: var(--bg-color, #fff);
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  overflow: auto;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* FMC Result Modal Header */
.fmc-result-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid var(--border-color, #ddd);
}

/* FMC Result Modal Title */
.fmc-result-modal-title {
  font-size: 1.2rem;
  font-weight: bold;
  color: var(--text-color, #333);
}

/* FMC Result Modal Close Button */
.fmc-result-modal-close {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  color: var(--text-color-secondary, #666);
}

/* FMC Result Content */
.fmc-result-content {
  padding: 15px;
}

/* FMC Result Move Count */
.fmc-result-move-count {
  font-size: 2rem;
  font-weight: bold;
  text-align: center;
  margin-bottom: 15px;
  color: var(--text-color, #333);
}

/* FMC Result Time */
.fmc-result-time {
  font-size: 1.2rem;
  text-align: center;
  margin-bottom: 10px;
  color: var(--text-color, #333);
}

/* FMC Result Validation */
.fmc-result-validation {
  text-align: center;
  margin-bottom: 15px;
  padding: 8px;
  border-radius: 4px;
  font-weight: bold;
}

.fmc-result-valid {
  background-color: rgba(46, 204, 113, 0.2);
  color: #27ae60;
}

.fmc-result-dnf {
  background-color: rgba(231, 76, 60, 0.2);
  color: #e74c3c;
}

.fmc-verification-note {
  font-size: 0.8rem;
  font-weight: normal;
  margin-top: 5px;
  opacity: 0.8;
}

.fmc-validation-link {
  display: inline-block;
  margin-top: 5px;
  color: var(--primary-color);
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: normal;
}

.fmc-validation-link:hover {
  text-decoration: underline;
}

.fmc-validation-details {
  margin: 15px 0;
  padding: 15px;
  border: 1px solid rgba(165, 120, 101, 0.3);
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.05);
}

.fmc-validation-status {
  font-size: 1.1rem;
  font-weight: bold;
  margin-bottom: 10px;
  text-align: center;
}

.fmc-validation-success {
  color: #2ecc71;
}

.fmc-validation-failed {
  color: #e74c3c;
}

.fmc-validation-reason {
  font-size: 0.95rem;
  margin: 10px 0;
  color: var(--text-color);
  text-align: center;
}

.fmc-validation-details-item {
  display: flex;
  justify-content: space-between;
  margin: 8px 0;
  font-size: 0.95rem;
}

.fmc-validation-label {
  font-weight: bold;
  color: var(--text-color);
}

.fmc-validation-value {
  color: var(--primary-color);
}

.fmc-validation-twizzle {
  text-align: center;
  margin: 15px 0 5px 0;
}

.fmc-twizzle-button {
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 15px;
  font-size: 0.9rem;
  cursor: pointer;
  margin: 10px auto 0;
  transition: background-color 0.2s;
  display: block;
  width: 80%;
  max-width: 200px;
}

.fmc-twizzle-button:hover {
  background-color: #2980b9;
}

.fmc-result-button-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.fmc-result-ok-button {
  background-color: var(--primary-color, #a57865);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 20px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.fmc-result-ok-button:hover {
  background-color: var(--primary-color-dark, #8a6253);
}

/* Ensure OK button is visible in light mode */
[data-theme="light"] .fmc-result-ok-button,
.light-mode .fmc-result-ok-button {
  background-color: #a57865;
  color: white;
}

[data-theme="light"] .fmc-result-ok-button:hover,
.light-mode .fmc-result-ok-button:hover {
  background-color: #8a6253;
}

/* FMC Result Solution */
.fmc-result-solution {
  font-family: monospace;
  padding: 10px;
  background-color: var(--input-bg, #f5f5f5);
  border: 1px solid var(--border-color, #ddd);
  border-radius: 4px;
  white-space: pre-wrap;
  word-break: break-word;
  margin-top: 15px;
  color: var(--text-color, #333);
}

/* Dark mode for solution display */
[data-theme="dark"] .fmc-result-solution,
.dark-mode .fmc-result-solution {
  background-color: var(--input-bg-dark, #2a2a2a);
  border-color: var(--border-color-dark, #555);
  color: var(--text-color-dark, #e0e0e0);
}

/* Dark Mode Keyboard Styling */
[data-theme="dark"] .fmc-key,
.dark-mode .fmc-key {
  background-color: #6b6b6b !important;
  color: #fff !important;
  border-color: #555 !important;
}

[data-theme="dark"] .fmc-key:hover,
.dark-mode .fmc-key:hover {
  background-color: #7a7a7a !important;
}

/* Dark mode special buttons */
[data-theme="dark"] .fmc-key[data-special="true"],
[data-theme="dark"] .fmc-keyboard-toggle,
.dark-mode .fmc-key[data-special="true"],
.dark-mode .fmc-keyboard-toggle {
  background-color: #464646 !important;
  color: #fff !important;
  border-color: #464646 !important;
}

[data-theme="dark"] .fmc-key[data-special="true"]:hover,
[data-theme="dark"] .fmc-keyboard-toggle:hover,
.dark-mode .fmc-key[data-special="true"]:hover,
.dark-mode .fmc-keyboard-toggle:hover {
  background-color: #555555 !important;
}

/* Dark mode timer and move count cells (like normal buttons) */
[data-theme="dark"] .fmc-keyboard-timer-cell,
[data-theme="dark"] .fmc-keyboard-move-count-cell,
.dark-mode .fmc-keyboard-timer-cell,
.dark-mode .fmc-keyboard-move-count-cell {
  background-color: #6b6b6b !important;
  border-color: #555 !important;
}

[data-theme="dark"] .fmc-keyboard-timer,
[data-theme="dark"] .fmc-keyboard-move-count,
.dark-mode .fmc-keyboard-timer,
.dark-mode .fmc-keyboard-move-count {
  color: #fff !important;
}

/* Dark mode other elements */
[data-theme="dark"] .fmc-solution-input,
.dark-mode .fmc-solution-input {
  background-color: #2b2b2b;
  color: #fff;
  border-color: #555;
}

[data-theme="dark"] .fmc-move-count-display,
.dark-mode .fmc-move-count-display {
  background-color: rgba(0, 0, 0, 0.7);
  color: #fff;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .fmc-key {
    padding: 10px 6px;
    font-size: 0.9rem;
  }

  .fmc-keyboard-timer,
  .fmc-keyboard-move-count {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .fmc-solution-input {
    min-height: 100px;
  }

  .fmc-keyboard-timer-cell,
  .fmc-keyboard-move-count-cell {
    padding: 5px;
  }
}
