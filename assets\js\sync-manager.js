// Google Drive Sync Manager for scTimer
// Handles authentication, file operations, and sync functionality

import { showConfirm, showAlert } from "./modal-manager.js";

class SyncManager {
  constructor() {
    this.isInitialized = false;
    this.isSignedIn = false;
    this.CLIENT_ID = ""; // Will be set from config
    this.API_KEY = ""; // Will be set from config
    this.DISCOVERY_DOC =
      "https://www.googleapis.com/discovery/v1/apis/drive/v3/rest";
    this.SCOPES = "https://www.googleapis.com/auth/drive.file";
    this.SYNC_FILE_NAME = "scTimer-data.json";
    this.autoSyncEnabled = false;
    this.syncInProgress = false;
    this.accessToken = null;
    this.tokenClient = null;
    this.autoSyncTimeout = null;
    this.syncCheckInterval = null;
    this.lastSyncTimestamp = null;
    this.isOnline = navigator.onLine;
    this.pendingSync = false;

    // Listen for online/offline events
    window.addEventListener("online", () => {
      console.log("Device came online");
      this.isOnline = true;
      this.handleOnlineStatusChange();
    });

    window.addEventListener("offline", () => {
      console.log("Device went offline");
      this.isOnline = false;
      this.handleOnlineStatusChange();
    });

    // Bind methods
    this.init = this.init.bind(this);
    this.signIn = this.signIn.bind(this);
    this.signOut = this.signOut.bind(this);
    this.uploadData = this.uploadData.bind(this);
    this.downloadData = this.downloadData.bind(this);
  }

  // Initialize Google Drive API
  async init() {
    try {
      // Load configuration
      await this.loadConfig();

      if (!this.CLIENT_ID) {
        console.warn("Google Drive sync: CLIENT_ID not configured");
        return false;
      }

      // Wait for both gapi and google identity services to load
      await this.waitForGapi();
      await this.waitForGIS();

      // Initialize Google API client
      await new Promise((resolve, reject) => {
        gapi.load("client", async () => {
          try {
            await gapi.client.init({
              apiKey: this.API_KEY,
              discoveryDocs: [this.DISCOVERY_DOC],
            });

            // Initialize Google Identity Services token client
            this.tokenClient = google.accounts.oauth2.initTokenClient({
              client_id: this.CLIENT_ID,
              scope: this.SCOPES,
              callback: (response) => {
                if (response.error) {
                  console.error("Token client error:", response);
                  this.updateSyncStatus("Authentication failed", "error");
                  return;
                }

                this.accessToken = response.access_token;
                this.isSignedIn = true;

                // Store token and expiration time
                const expiresAt = Date.now() + response.expires_in * 1000;
                localStorage.setItem("scTimer-gdrive-token", this.accessToken);
                localStorage.setItem(
                  "scTimer-gdrive-token-expires",
                  expiresAt.toString()
                );

                this.updateAuthStatus();
              },
            });

            // Check for existing valid token
            this.loadStoredToken();

            this.isInitialized = true;
            this.updateAuthStatus();
            resolve();
          } catch (error) {
            reject(error);
          }
        });
      });

      return true;
    } catch (error) {
      console.error("Failed to initialize Google Drive API:", error);
      this.updateSyncStatus("Error initializing Google Drive API", "error");
      return false;
    }
  }

  // Wait for gapi to be available
  waitForGapi() {
    return new Promise((resolve) => {
      const checkGapi = () => {
        if (typeof gapi !== "undefined") {
          resolve();
        } else {
          setTimeout(checkGapi, 100);
        }
      };
      checkGapi();
    });
  }

  // Wait for Google Identity Services to be available
  waitForGIS() {
    return new Promise((resolve) => {
      const checkGIS = () => {
        if (typeof google !== "undefined" && google.accounts) {
          resolve();
        } else {
          setTimeout(checkGIS, 100);
        }
      };
      checkGIS();
    });
  }

  // Load stored token from localStorage
  loadStoredToken() {
    const storedToken = localStorage.getItem("scTimer-gdrive-token");
    const expiresAt = localStorage.getItem("scTimer-gdrive-token-expires");

    if (storedToken && expiresAt) {
      const now = Date.now();
      const expiration = parseInt(expiresAt);

      if (now < expiration) {
        // Token is still valid
        this.accessToken = storedToken;
        this.isSignedIn = true;
        console.log("Restored valid Google Drive token from storage");
        return true;
      } else {
        // Token expired, clear it
        this.clearStoredToken();
        console.log("Stored Google Drive token expired, cleared");
      }
    }
    return false;
  }

  // Clear stored token
  clearStoredToken() {
    localStorage.removeItem("scTimer-gdrive-token");
    localStorage.removeItem("scTimer-gdrive-token-expires");
  }

  // Load configuration from localStorage or config file
  async loadConfig() {
    // Try to load from localStorage first (user can set it manually)
    this.CLIENT_ID = localStorage.getItem("scTimer-gdrive-client-id") || "";
    this.API_KEY = localStorage.getItem("scTimer-gdrive-api-key") || "";

    // If not in localStorage, try to load from config file
    if (!this.CLIENT_ID) {
      try {
        const response = await fetch("config/gdrive-config.json");
        if (response.ok) {
          const config = await response.json();
          this.CLIENT_ID = config.clientId || "";
          this.API_KEY = config.apiKey || "";
        }
      } catch (error) {
        console.log(
          "No Google Drive config file found, using localStorage values"
        );
      }
    }
  }

  // Sign in to Google Drive
  async signIn() {
    if (!this.isInitialized) {
      const initialized = await this.init();
      if (!initialized) return false;
    }

    try {
      if (!this.tokenClient) {
        console.error("Token client not available");
        this.updateSyncStatus("Authentication not available", "error");
        return false;
      }

      // Check if already signed in with valid token
      if (this.isSignedIn && this.accessToken) {
        console.log("Already signed in with valid token");
        this.updateAuthStatus();
        return true;
      }

      // Try to load stored token first
      if (this.loadStoredToken()) {
        console.log("Restored authentication from stored token");
        this.updateAuthStatus();
        return true;
      }

      // Request new access token
      this.tokenClient.requestAccessToken({
        prompt: "consent",
      });

      return true;
    } catch (error) {
      console.error("Sign in failed:", error);
      this.updateSyncStatus("Sign in failed", "error");
      return false;
    }
  }

  // Sign out from Google Drive
  async signOut() {
    if (!this.isInitialized) return;

    try {
      if (this.accessToken) {
        // Revoke the access token
        google.accounts.oauth2.revoke(this.accessToken);
      }

      this.accessToken = null;
      this.isSignedIn = false;
      this.autoSyncEnabled = false;

      // Clear stored tokens and settings
      this.clearStoredToken();
      localStorage.removeItem("scTimer-auto-sync");

      // Stop sync checking
      this.stopSyncChecking();

      this.updateAuthStatus();
    } catch (error) {
      console.error("Sign out failed:", error);
    }
  }

  // Update authentication status
  updateAuthStatus() {
    if (!this.isInitialized) return;

    try {
      const statusElement = document.getElementById("sync-status");
      const connectBtn = document.getElementById("sync-connect-btn");
      const disconnectBtn = document.getElementById("sync-disconnect-btn");
      const syncActions = document.getElementById("sync-actions");

      if (this.isSignedIn && this.accessToken) {
        // For now, we'll show a generic connected message
        // In a full implementation, you could decode the JWT token to get user info
        this.updateSyncStatus("Connected to Google Drive", "connected");
        if (connectBtn) connectBtn.style.display = "none";
        if (disconnectBtn) disconnectBtn.style.display = "inline-flex";
        if (syncActions) syncActions.style.display = "flex";

        // Show auto-sync option
        const syncAutoOption = document.getElementById("sync-auto-option");
        if (syncAutoOption) syncAutoOption.style.display = "flex";

        // Load auto-sync preference
        this.autoSyncEnabled =
          localStorage.getItem("scTimer-auto-sync") === "true";

        // Update auto-sync checkbox
        const autoSyncCheckbox = document.getElementById("sync-auto-checkbox");
        if (autoSyncCheckbox) {
          autoSyncCheckbox.checked = this.autoSyncEnabled;
        }

        // Start sync checking if auto-sync is enabled
        if (this.autoSyncEnabled) {
          this.startSyncChecking();
        }
      } else {
        this.updateSyncStatus("Not connected", "");
        if (connectBtn) connectBtn.style.display = "inline-flex";
        if (disconnectBtn) disconnectBtn.style.display = "none";
        if (syncActions) syncActions.style.display = "none";

        // Hide auto-sync option
        const syncAutoOption = document.getElementById("sync-auto-option");
        if (syncAutoOption) syncAutoOption.style.display = "none";
      }
    } catch (error) {
      console.error("Error updating auth status:", error);
      this.updateSyncStatus("Not connected", "");
      this.showSyncActions(false);
    }
  }

  // Helper method to show/hide sync actions
  showSyncActions(show) {
    const connectBtn = document.getElementById("sync-connect-btn");
    const disconnectBtn = document.getElementById("sync-disconnect-btn");
    const syncActions = document.getElementById("sync-actions");

    if (show) {
      connectBtn.style.display = "none";
      disconnectBtn.style.display = "inline-block";
      syncActions.style.display = "flex";
    } else {
      connectBtn.style.display = "inline-block";
      disconnectBtn.style.display = "none";
      syncActions.style.display = "none";
    }
  }

  // Update sync status display
  updateSyncStatus(message, type = "") {
    const statusElement = document.getElementById("sync-status");
    if (statusElement) {
      statusElement.textContent = message;
      statusElement.className = `sync-status ${type}`;
    }
  }

  // Show sync loading spinner
  showSyncLoading() {
    const statusElement = document.getElementById("sync-status");
    if (statusElement) {
      const translations = window.i18nModule?.translations || {};
      const syncTranslations = translations.sync || {};

      statusElement.innerHTML = `
        <div class="sync-loading-container">
          <div class="scramble-loader"></div>
          <span>${syncTranslations.syncing || "Syncing..."}</span>
        </div>
      `;
      statusElement.className = "sync-status syncing";
    }
  }

  // Hide sync loading spinner
  hideSyncLoading() {
    const statusElement = document.getElementById("sync-status");
    if (statusElement) {
      // Restore normal status structure
      statusElement.innerHTML = "<span></span>";
    }
  }

  // Toggle auto-sync
  async toggleAutoSync() {
    this.autoSyncEnabled = !this.autoSyncEnabled;
    localStorage.setItem("scTimer-auto-sync", this.autoSyncEnabled.toString());

    // Update checkbox state
    const autoSyncCheckbox = document.getElementById("sync-auto-checkbox");
    if (autoSyncCheckbox) {
      autoSyncCheckbox.checked = this.autoSyncEnabled;
    }

    if (this.autoSyncEnabled && this.isSignedIn && this.isOnline) {
      console.log("Auto-sync enabled, performing initial sync...");
      this.showSyncLoading();

      try {
        // Perform initial bidirectional sync
        await this.performFullSync();
        this.startSyncChecking();
        this.hideSyncLoading();
        this.updateSyncStatus("Auto-sync enabled", "connected");
      } catch (error) {
        console.error("Initial sync failed:", error);
        this.hideSyncLoading();
        this.updateSyncStatus("Initial sync failed", "error");
        // Still start checking even if initial sync failed
        this.startSyncChecking();
      }
    } else if (this.autoSyncEnabled && this.isSignedIn && !this.isOnline) {
      console.log("Auto-sync enabled but offline, will sync when online");
      this.updateSyncStatus("Auto-sync enabled (offline)", "");
      this.pendingSync = true;
    } else if (this.autoSyncEnabled && !this.isSignedIn) {
      this.updateSyncStatus("Auto-sync enabled (not connected)", "");
    } else {
      console.log("Auto-sync disabled, stopping sync checking...");
      this.stopSyncChecking();
      this.hideSyncLoading();
      this.updateSyncStatus("Auto-sync disabled", "");
    }
  }

  // Get all scTimer data for sync
  getAllData() {
    const data = {
      times: JSON.parse(localStorage.getItem("scTimer-times") || "{}"),
      customSessions: JSON.parse(
        localStorage.getItem("scTimer-customSessions") || "[]"
      ),
      settings: {
        language: localStorage.getItem("scTimer-language"),
        decimalPlaces: localStorage.getItem("scTimer-decimalPlaces"),
        lastEvent: localStorage.getItem("scTimer-lastEvent"),
        lastEventText: localStorage.getItem("scTimer-lastEventText"),
        lastSessionId: localStorage.getItem("scTimer-lastSessionId"),
        // Add other settings as needed
      },
      timestamp: new Date().toISOString(),
      version: "1.0",
    };
    return data;
  }

  // Set all scTimer data from sync with intelligent merging
  setAllData(data, mergeMode = false) {
    if (!data || typeof data !== "object") return false;

    try {
      // Handle times with intelligent merging
      if (data.times) {
        if (mergeMode) {
          const mergedTimes = this.mergeTimes(data.times);
          localStorage.setItem("scTimer-times", JSON.stringify(mergedTimes));
        } else {
          localStorage.setItem("scTimer-times", JSON.stringify(data.times));
        }
      }

      // Handle custom sessions with merging
      if (data.customSessions) {
        if (mergeMode) {
          const mergedSessions = this.mergeSessions(data.customSessions);
          localStorage.setItem(
            "scTimer-customSessions",
            JSON.stringify(mergedSessions)
          );
        } else {
          localStorage.setItem(
            "scTimer-customSessions",
            JSON.stringify(data.customSessions)
          );
        }
      }

      // Restore settings (always overwrite settings, no merging needed)
      if (data.settings) {
        Object.entries(data.settings).forEach(([key, value]) => {
          if (value !== null && value !== undefined) {
            localStorage.setItem(`scTimer-${key}`, value);
          }
        });
      }

      return true;
    } catch (error) {
      console.error("Error setting sync data:", error);
      return false;
    }
  }

  // Intelligent merge of times from remote and local data
  mergeTimes(remoteTimes) {
    const localTimes = JSON.parse(
      localStorage.getItem("scTimer-times") || "{}"
    );
    const merged = { ...localTimes };

    console.log("Merging times:", {
      localEvents: Object.keys(localTimes),
      remoteEvents: Object.keys(remoteTimes),
    });

    // Process each event/session from remote data
    Object.entries(remoteTimes).forEach(([eventKey, remoteSolves]) => {
      if (!Array.isArray(remoteSolves)) return;

      const localSolves = merged[eventKey] || [];
      const mergedSolves = this.mergeSolveArrays(
        localSolves,
        remoteSolves,
        eventKey
      );

      if (mergedSolves.length > 0) {
        merged[eventKey] = mergedSolves;
      }
    });

    return merged;
  }

  // Merge two arrays of solves intelligently
  mergeSolveArrays(localSolves, remoteSolves, eventKey) {
    console.log(`Merging solves for ${eventKey}:`, {
      localCount: localSolves.length,
      remoteCount: remoteSolves.length,
    });

    // Create a map of existing solves by unique identifier
    const solveMap = new Map();
    const conflicts = [];

    // Add local solves to map
    localSolves.forEach((solve, index) => {
      const key = this.getSolveKey(solve);
      if (solveMap.has(key)) {
        // Handle duplicate keys in local data
        const existing = solveMap.get(key);
        if (this.isSolveNewer(solve, existing.solve)) {
          solveMap.set(key, { solve, source: "local", index });
        }
      } else {
        solveMap.set(key, { solve, source: "local", index });
      }
    });

    // Process remote solves
    remoteSolves.forEach((remoteSolve, index) => {
      const key = this.getSolveKey(remoteSolve);

      if (solveMap.has(key)) {
        const existing = solveMap.get(key);

        // Check if remote solve is different/newer
        if (!this.areSolvesEqual(existing.solve, remoteSolve)) {
          if (this.isSolveNewer(remoteSolve, existing.solve)) {
            console.log(`Updating solve from remote:`, { key, remoteSolve });
            solveMap.set(key, { solve: remoteSolve, source: "remote", index });
          } else {
            console.log(`Keeping local solve (newer):`, {
              key,
              local: existing.solve,
            });
            conflicts.push({ local: existing.solve, remote: remoteSolve, key });
          }
        }
      } else {
        // New solve from remote
        console.log(`Adding new solve from remote:`, { key, remoteSolve });
        solveMap.set(key, { solve: remoteSolve, source: "remote", index });
      }
    });

    // Convert back to array and sort by date (newest first)
    const mergedArray = Array.from(solveMap.values())
      .map((item) => item.solve)
      .sort((a, b) => new Date(b.date) - new Date(a.date));

    console.log(`Merge complete for ${eventKey}:`, {
      finalCount: mergedArray.length,
      conflicts: conflicts.length,
    });

    if (conflicts.length > 0) {
      console.warn(
        `Resolved ${conflicts.length} conflicts for ${eventKey}`,
        conflicts
      );
    }

    return mergedArray;
  }

  // Generate unique key for a solve
  getSolveKey(solve) {
    // Use date + time + scramble as unique identifier
    // This handles cases where same time was achieved at same moment with same scramble
    const date = solve.date || "";
    const time = solve.time || 0;
    const scramble = (solve.scramble || "").substring(0, 50); // First 50 chars of scramble
    return `${date}_${time}_${scramble}`;
  }

  // Check if two solves are equal
  areSolvesEqual(solve1, solve2) {
    return (
      solve1.time === solve2.time &&
      solve1.date === solve2.date &&
      solve1.scramble === solve2.scramble &&
      solve1.penalty === solve2.penalty &&
      solve1.comment === solve2.comment
    );
  }

  // Determine which solve is newer (for conflict resolution)
  isSolveNewer(solve1, solve2) {
    const date1 = new Date(solve1.date || 0);
    const date2 = new Date(solve2.date || 0);

    // If dates are different, newer date wins
    if (date1.getTime() !== date2.getTime()) {
      return date1 > date2;
    }

    // If dates are same, prefer solve with more data (comments, penalties, etc.)
    const score1 = this.getSolveDataScore(solve1);
    const score2 = this.getSolveDataScore(solve2);

    return score1 >= score2;
  }

  // Score solve based on amount of data (for conflict resolution)
  getSolveDataScore(solve) {
    let score = 0;
    if (solve.comment && solve.comment.trim()) score += 2;
    if (solve.penalty) score += 1;
    if (solve.result) score += 1;
    if (solve.scramble && solve.scramble.length > 10) score += 1;
    return score;
  }

  // Merge custom sessions
  mergeSessions(remoteSessions) {
    const localSessions = JSON.parse(
      localStorage.getItem("scTimer-customSessions") || "[]"
    );

    console.log("Merging sessions:", {
      localCount: localSessions.length,
      remoteCount: remoteSessions.length,
    });

    // Create a map of sessions by ID
    const sessionMap = new Map();

    // Add local sessions
    localSessions.forEach((session) => {
      if (session.id) {
        sessionMap.set(session.id, { ...session, source: "local" });
      }
    });

    // Process remote sessions
    remoteSessions.forEach((remoteSession) => {
      if (!remoteSession.id) return;

      if (sessionMap.has(remoteSession.id)) {
        const localSession = sessionMap.get(remoteSession.id);

        // Compare modification dates if available
        const remoteDate = new Date(
          remoteSession.lastModified || remoteSession.created || 0
        );
        const localDate = new Date(
          localSession.lastModified || localSession.created || 0
        );

        if (remoteDate >= localDate) {
          console.log(`Updating session from remote: ${remoteSession.name}`);
          sessionMap.set(remoteSession.id, {
            ...remoteSession,
            source: "remote",
          });
        }
      } else {
        console.log(`Adding new session from remote: ${remoteSession.name}`);
        sessionMap.set(remoteSession.id, {
          ...remoteSession,
          source: "remote",
        });
      }
    });

    const mergedSessions = Array.from(sessionMap.values())
      .map((session) => {
        const { source, ...sessionData } = session;
        return sessionData;
      })
      .sort((a, b) => new Date(b.created || 0) - new Date(a.created || 0));

    console.log(
      `Session merge complete: ${mergedSessions.length} total sessions`
    );
    return mergedSessions;
  }

  // Upload data to Google Drive
  async uploadData() {
    if (!this.isSignedIn || this.syncInProgress || !this.accessToken)
      return false;

    this.syncInProgress = true;
    this.updateSyncStatus("Uploading...", "");

    try {
      const data = this.getAllData();
      const content = JSON.stringify(data, null, 2);

      // Check if file already exists
      const existingFile = await this.findSyncFile();

      let response;
      if (existingFile) {
        // Update existing file
        response = await fetch(
          `https://www.googleapis.com/upload/drive/v3/files/${existingFile.id}?uploadType=media`,
          {
            method: "PATCH",
            headers: {
              Authorization: `Bearer ${this.accessToken}`,
              "Content-Type": "application/json",
            },
            body: content,
          }
        );
      } else {
        // Create new file
        const multipartBody = this.createMultipartBody(
          this.SYNC_FILE_NAME,
          content
        );
        response = await fetch(
          "https://www.googleapis.com/upload/drive/v3/files?uploadType=multipart",
          {
            method: "POST",
            headers: {
              Authorization: `Bearer ${this.accessToken}`,
              "Content-Type": 'multipart/related; boundary="foo_bar_baz"',
            },
            body: multipartBody,
          }
        );
      }

      if (response.status === 200) {
        this.updateSyncStatus("Upload successful", "connected");
        return true;
      } else {
        throw new Error(`Upload failed with status ${response.status}`);
      }
    } catch (error) {
      console.error("Upload failed:", error);
      this.updateSyncStatus("Upload failed", "error");
      return false;
    } finally {
      this.syncInProgress = false;
    }
  }

  // Download data from Google Drive with smart merging
  async downloadData() {
    if (!this.isSignedIn || this.syncInProgress || !this.accessToken)
      return false;

    this.syncInProgress = true;
    this.updateSyncStatus("Downloading...", "");

    try {
      const file = await this.findSyncFile();
      if (!file) {
        this.updateSyncStatus("No sync file found", "error");
        return false;
      }

      const response = await fetch(
        `https://www.googleapis.com/drive/v3/files/${file.id}?alt=media`,
        {
          method: "GET",
          headers: {
            Authorization: `Bearer ${this.accessToken}`,
          },
        }
      );

      if (response.ok) {
        const content = await response.text();
        const data = JSON.parse(content);

        // Get translations
        const translations = window.i18nModule?.translations || {};
        const syncTranslations = translations.sync || {};

        // Ask user if they want to merge or replace using custom modal
        const mergeMessage =
          syncTranslations.downloadMergeConfirm ||
          "Choose how to handle the downloaded data:\n\nOK = Smart merge (recommended - combines data safely)\nCancel = Replace all (overwrites local data)";

        const result = await new Promise((resolve) => {
          showConfirm(
            mergeMessage,
            // On confirm (Smart merge)
            () => {
              if (this.setAllData(data, true)) {
                this.setLocalTimestamp(new Date(file.modifiedTime).getTime());
                this.updateSyncStatus(
                  "Download merged successfully",
                  "connected"
                );

                // Refresh UI without page reload
                if (
                  window.loadTimes &&
                  typeof window.loadTimes === "function"
                ) {
                  window.loadTimes();
                }
                resolve(true);
              } else {
                this.updateSyncStatus(
                  "Failed to merge downloaded data",
                  "error"
                );
                resolve(false);
              }
            },
            // On cancel (Replace mode)
            () => {
              const replaceMessage =
                syncTranslations.downloadConfirm ||
                "This will replace your local data with data from Google Drive. Continue?";

              showConfirm(
                replaceMessage,
                // On confirm replace
                () => {
                  if (this.setAllData(data, false)) {
                    this.setLocalTimestamp(
                      new Date(file.modifiedTime).getTime()
                    );
                    this.updateSyncStatus("Download successful", "connected");

                    // Ask for page reload for replace mode
                    const reloadMessage =
                      syncTranslations.reloadConfirm ||
                      "Reload page to see changes?";

                    showConfirm(
                      reloadMessage,
                      () => {
                        window.location.reload();
                      },
                      () => {
                        // User chose not to reload, that's fine
                      }
                    );
                    resolve(true);
                  } else {
                    this.updateSyncStatus(
                      "Failed to apply downloaded data",
                      "error"
                    );
                    resolve(false);
                  }
                },
                // On cancel replace
                () => {
                  this.updateSyncStatus("Download cancelled", "");
                  resolve(false);
                }
              );
            }
          );
        });

        return result;
      } else {
        throw new Error(`Download failed with status ${response.status}`);
      }
    } catch (error) {
      console.error("Download failed:", error);
      this.updateSyncStatus("Download failed", "error");
      return false;
    } finally {
      this.syncInProgress = false;
    }
  }

  // Find existing sync file
  async findSyncFile() {
    if (!this.accessToken) return null;

    try {
      const query = encodeURIComponent(
        `name='${this.SYNC_FILE_NAME}' and trashed=false`
      );
      const response = await fetch(
        `https://www.googleapis.com/drive/v3/files?q=${query}&spaces=drive&fields=files(id,name,modifiedTime)`,
        {
          method: "GET",
          headers: {
            Authorization: `Bearer ${this.accessToken}`,
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        if (data.files && data.files.length > 0) {
          return data.files[0];
        }
      }
      return null;
    } catch (error) {
      console.error("Error finding sync file:", error);
      return null;
    }
  }

  // Create multipart body for file upload
  createMultipartBody(filename, content) {
    const delimiter = "foo_bar_baz";
    const close_delim = `\r\n--${delimiter}--`;

    const metadata = {
      name: filename,
      parents: [], // Store in root folder
    };

    const multipartRequestBody =
      `--${delimiter}\r\n` +
      "Content-Type: application/json\r\n\r\n" +
      JSON.stringify(metadata) +
      "\r\n" +
      `--${delimiter}\r\n` +
      "Content-Type: application/json\r\n\r\n" +
      content +
      close_delim;

    return multipartRequestBody;
  }

  // Check for remote changes and download if newer
  async checkForRemoteChanges() {
    if (
      !this.autoSyncEnabled ||
      !this.isSignedIn ||
      this.syncInProgress ||
      !this.accessToken
    ) {
      return false;
    }

    try {
      const file = await this.findSyncFile();
      if (!file) {
        console.log("No remote sync file found");
        return false;
      }

      // Get file modification time
      const remoteModified = new Date(file.modifiedTime).getTime();
      const localTimestamp = this.getLocalTimestamp();

      console.log("Sync check:", {
        remoteModified: new Date(remoteModified).toISOString(),
        localTimestamp: localTimestamp
          ? new Date(localTimestamp).toISOString()
          : "none",
        isRemoteNewer: remoteModified > (localTimestamp || 0),
      });

      // If remote file is newer, download it
      if (remoteModified > (localTimestamp || 0)) {
        console.log("Remote changes detected, downloading...");
        await this.downloadDataSilently();
        return true;
      }

      return false;
    } catch (error) {
      console.error("Error checking for remote changes:", error);
      return false;
    }
  }

  // Download data silently without user confirmation
  async downloadDataSilently() {
    if (!this.isSignedIn || this.syncInProgress || !this.accessToken)
      return false;

    this.syncInProgress = true;
    console.log("Silently downloading remote changes...");

    try {
      const file = await this.findSyncFile();
      if (!file) {
        return false;
      }

      const response = await fetch(
        `https://www.googleapis.com/drive/v3/files/${file.id}?alt=media`,
        {
          method: "GET",
          headers: {
            Authorization: `Bearer ${this.accessToken}`,
          },
        }
      );

      if (response.ok) {
        const content = await response.text();
        const data = JSON.parse(content);

        if (this.setAllData(data, true)) {
          // Use merge mode for silent downloads
          // Update local timestamp
          this.setLocalTimestamp(new Date(file.modifiedTime).getTime());

          console.log("Remote changes merged successfully");

          // Refresh the UI to show new data
          if (window.loadTimes && typeof window.loadTimes === "function") {
            window.loadTimes();
          }

          return true;
        }
      }
      return false;
    } catch (error) {
      console.error("Silent download failed:", error);
      return false;
    } finally {
      this.syncInProgress = false;
    }
  }

  // Get local data timestamp
  getLocalTimestamp() {
    const timestamp = localStorage.getItem("scTimer-sync-timestamp");
    return timestamp ? parseInt(timestamp) : null;
  }

  // Set local data timestamp
  setLocalTimestamp(timestamp) {
    localStorage.setItem("scTimer-sync-timestamp", timestamp.toString());
    this.lastSyncTimestamp = timestamp;
  }

  // Handle online/offline status changes
  handleOnlineStatusChange() {
    if (
      this.isOnline &&
      this.pendingSync &&
      this.autoSyncEnabled &&
      this.isSignedIn
    ) {
      console.log("Device back online, executing pending sync...");
      this.pendingSync = false;

      // Perform full bidirectional sync when coming back online
      setTimeout(async () => {
        await this.performFullSync();
      }, 2000); // Wait 2 seconds for connection to stabilize
    }

    // Update sync checking based on online status
    if (this.autoSyncEnabled && this.isSignedIn) {
      if (this.isOnline) {
        this.startSyncChecking();
      } else {
        this.stopSyncChecking();
      }
    }
  }

  // Perform full bidirectional sync (upload then check for remote changes)
  async performFullSync() {
    if (!this.autoSyncEnabled || !this.isSignedIn || !this.isOnline)
      return false;

    console.log("Performing full bidirectional sync...");

    try {
      // First upload local changes
      const uploaded = await this.uploadData();
      if (uploaded) {
        this.setLocalTimestamp(Date.now());
      }

      // Then check for and download remote changes
      const hasRemoteChanges = await this.checkForRemoteChanges();

      console.log("Full sync completed successfully");
      return true;
    } catch (error) {
      console.error("Full sync failed:", error);
      throw error; // Re-throw to let caller handle it
    }
  }

  // Auto-sync when data changes (call this from main timer)
  async autoSync() {
    console.log("Auto-sync triggered:", {
      autoSyncEnabled: this.autoSyncEnabled,
      isSignedIn: this.isSignedIn,
      syncInProgress: this.syncInProgress,
      hasAccessToken: !!this.accessToken,
      isOnline: this.isOnline,
    });

    if (
      this.autoSyncEnabled &&
      this.isSignedIn &&
      !this.syncInProgress &&
      this.accessToken
    ) {
      if (this.isOnline) {
        // Add a small delay to debounce rapid changes
        clearTimeout(this.autoSyncTimeout);
        this.autoSyncTimeout = setTimeout(async () => {
          console.log("Executing auto-sync upload...");
          const uploaded = await this.uploadData();
          if (uploaded) {
            // Update local timestamp after successful upload
            this.setLocalTimestamp(Date.now());
          }
        }, 1000); // Wait 1 second before syncing
      } else {
        // Mark that we have pending changes to sync when online
        console.log("Offline: marking sync as pending");
        this.pendingSync = true;
      }
    }
  }

  // Start periodic sync checking
  startSyncChecking() {
    if (this.syncCheckInterval) {
      clearInterval(this.syncCheckInterval);
    }

    if (this.autoSyncEnabled && this.isSignedIn && this.isOnline) {
      console.log("Starting periodic sync checking...");
      this.syncCheckInterval = setInterval(async () => {
        if (this.isOnline) {
          // Double-check online status
          await this.checkForRemoteChanges();
        }
      }, 10000); // Check every 10 seconds
    }
  }

  // Stop periodic sync checking
  stopSyncChecking() {
    if (this.syncCheckInterval) {
      clearInterval(this.syncCheckInterval);
      this.syncCheckInterval = null;
      console.log("Stopped periodic sync checking");
    }
  }
}

// Create global instance
const syncManager = new SyncManager();

// Initialize when DOM is loaded
document.addEventListener("DOMContentLoaded", async () => {
  // Set up event listeners
  const connectBtn = document.getElementById("sync-connect-btn");
  const disconnectBtn = document.getElementById("sync-disconnect-btn");
  const uploadBtn = document.getElementById("sync-upload-btn");
  const downloadBtn = document.getElementById("sync-download-btn");
  const autoSyncCheckbox = document.getElementById("sync-auto-checkbox");

  if (connectBtn) {
    connectBtn.addEventListener("click", () => syncManager.signIn());
  }

  if (disconnectBtn) {
    disconnectBtn.addEventListener("click", () => syncManager.signOut());
  }

  if (uploadBtn) {
    uploadBtn.addEventListener("click", () => syncManager.uploadData());
  }

  if (downloadBtn) {
    downloadBtn.addEventListener("click", () => syncManager.downloadData());
  }

  if (autoSyncCheckbox) {
    autoSyncCheckbox.addEventListener("change", () =>
      syncManager.toggleAutoSync()
    );
  }

  // Initialize the sync manager
  await syncManager.init();
});

// Make sync manager available globally
window.syncManager = syncManager;

export default syncManager;
