// Google Drive Sync Manager for scTimer
// Handles authentication, file operations, and sync functionality

class SyncManager {
  constructor() {
    this.isInitialized = false;
    this.isSignedIn = false;
    this.gapi = null;
    this.CLIENT_ID = ""; // Will be set from config
    this.API_KEY = ""; // Will be set from config
    this.DISCOVERY_DOC =
      "https://www.googleapis.com/discovery/v1/apis/drive/v3/rest";
    this.SCOPES = "https://www.googleapis.com/auth/drive.file";
    this.SYNC_FILE_NAME = "scTimer-data.json";
    this.autoSyncEnabled = false;
    this.syncInProgress = false;

    // Bind methods
    this.init = this.init.bind(this);
    this.signIn = this.signIn.bind(this);
    this.signOut = this.signOut.bind(this);
    this.uploadData = this.uploadData.bind(this);
    this.downloadData = this.downloadData.bind(this);
  }

  // Initialize Google Drive API
  async init() {
    try {
      // Load configuration
      await this.loadConfig();

      if (!this.CLIENT_ID) {
        console.warn("Google Drive sync: CLIENT_ID not configured");
        return false;
      }

      // Wait for gapi to load
      await this.waitForGapi();

      // Initialize the API with proper promise handling
      await new Promise((resolve, reject) => {
        gapi.load("client:auth2", async () => {
          try {
            // Initialize client first
            await gapi.client.init({
              apiKey: this.API_KEY,
              clientId: this.CLIENT_ID,
              discoveryDocs: [this.DISCOVERY_DOC],
              scope: this.SCOPES,
            });

            // Check if auth2 is already initialized
            let authInstance;
            try {
              authInstance = gapi.auth2.getAuthInstance();
            } catch (e) {
              // If not initialized, initialize it
              authInstance = await gapi.auth2.init({
                client_id: this.CLIENT_ID,
              });
            }

            this.isInitialized = true;
            this.updateAuthStatus();

            // Set up auth status listener
            authInstance.isSignedIn.listen(this.updateAuthStatus.bind(this));

            resolve();
          } catch (error) {
            reject(error);
          }
        });
      });

      return true;
    } catch (error) {
      console.error("Failed to initialize Google Drive API:", error);
      this.updateSyncStatus("Error initializing Google Drive API", "error");
      return false;
    }
  }

  // Wait for gapi to be available
  waitForGapi() {
    return new Promise((resolve) => {
      const checkGapi = () => {
        if (typeof gapi !== "undefined") {
          resolve();
        } else {
          setTimeout(checkGapi, 100);
        }
      };
      checkGapi();
    });
  }

  // Load configuration from localStorage or config file
  async loadConfig() {
    // Try to load from localStorage first (user can set it manually)
    this.CLIENT_ID = localStorage.getItem("scTimer-gdrive-client-id") || "";
    this.API_KEY = localStorage.getItem("scTimer-gdrive-api-key") || "";

    // If not in localStorage, try to load from config file
    if (!this.CLIENT_ID) {
      try {
        const response = await fetch("config/gdrive-config.json");
        if (response.ok) {
          const config = await response.json();
          this.CLIENT_ID = config.clientId || "";
          this.API_KEY = config.apiKey || "";
        }
      } catch (error) {
        console.log(
          "No Google Drive config file found, using localStorage values"
        );
      }
    }
  }

  // Sign in to Google Drive
  async signIn() {
    if (!this.isInitialized) {
      const initialized = await this.init();
      if (!initialized) return false;
    }

    try {
      const authInstance = gapi.auth2.getAuthInstance();
      if (!authInstance) {
        console.error("Auth instance not available");
        this.updateSyncStatus("Authentication not available", "error");
        return false;
      }

      // Check if already signed in
      if (authInstance.isSignedIn.get()) {
        console.log("Already signed in");
        this.updateAuthStatus();
        return true;
      }

      await authInstance.signIn({
        prompt: "select_account",
      });
      this.updateAuthStatus();
      return true;
    } catch (error) {
      console.error("Sign in failed:", error);
      this.updateSyncStatus("Sign in failed", "error");
      return false;
    }
  }

  // Sign out from Google Drive
  async signOut() {
    if (!this.isInitialized) return;

    try {
      const authInstance = gapi.auth2.getAuthInstance();
      await authInstance.signOut();
      this.autoSyncEnabled = false;
      localStorage.removeItem("scTimer-auto-sync");
      this.updateAutoSyncButton();
    } catch (error) {
      console.error("Sign out failed:", error);
    }
  }

  // Update authentication status
  updateAuthStatus() {
    if (!this.isInitialized) return;

    try {
      const authInstance = gapi.auth2.getAuthInstance();
      if (!authInstance) {
        this.updateSyncStatus("Not connected", "");
        this.showSyncActions(false);
        return;
      }

      this.isSignedIn = authInstance.isSignedIn.get();

      const statusElement = document.getElementById("sync-status");
      const connectBtn = document.getElementById("sync-connect-btn");
      const disconnectBtn = document.getElementById("sync-disconnect-btn");
      const syncActions = document.getElementById("sync-actions");

      if (this.isSignedIn) {
        const user = authInstance.currentUser.get();
        const profile = user.getBasicProfile();
        const email = profile.getEmail();

        this.updateSyncStatus(`Connected as ${email}`, "connected");
        connectBtn.style.display = "none";
        disconnectBtn.style.display = "inline-block";
        syncActions.style.display = "flex";

        // Load auto-sync preference
        this.autoSyncEnabled =
          localStorage.getItem("scTimer-auto-sync") === "true";
        this.updateAutoSyncButton();
      } else {
        this.updateSyncStatus("Not connected", "");
        connectBtn.style.display = "inline-block";
        disconnectBtn.style.display = "none";
        syncActions.style.display = "none";
      }
    } catch (error) {
      console.error("Error updating auth status:", error);
      this.updateSyncStatus("Not connected", "");
      this.showSyncActions(false);
    }
  }

  // Helper method to show/hide sync actions
  showSyncActions(show) {
    const connectBtn = document.getElementById("sync-connect-btn");
    const disconnectBtn = document.getElementById("sync-disconnect-btn");
    const syncActions = document.getElementById("sync-actions");

    if (show) {
      connectBtn.style.display = "none";
      disconnectBtn.style.display = "inline-block";
      syncActions.style.display = "flex";
    } else {
      connectBtn.style.display = "inline-block";
      disconnectBtn.style.display = "none";
      syncActions.style.display = "none";
    }
  }

  // Update sync status display
  updateSyncStatus(message, type = "") {
    const statusElement = document.getElementById("sync-status");
    if (statusElement) {
      statusElement.querySelector("span").textContent = message;
      statusElement.className = `sync-status ${type}`;
    }
  }

  // Update auto-sync button
  updateAutoSyncButton() {
    const autoBtn = document.getElementById("sync-auto-btn");
    if (autoBtn) {
      const translations = window.i18nModule?.translations || {};
      const syncTranslations = translations.sync || {};
      const status = this.autoSyncEnabled ? "On" : "Off";
      autoBtn.textContent = `${
        syncTranslations.autoSync || "Auto Sync"
      }: ${status}`;
    }
  }

  // Toggle auto-sync
  toggleAutoSync() {
    this.autoSyncEnabled = !this.autoSyncEnabled;
    localStorage.setItem("scTimer-auto-sync", this.autoSyncEnabled.toString());
    this.updateAutoSyncButton();

    if (this.autoSyncEnabled) {
      this.updateSyncStatus("Auto-sync enabled", "connected");
    }
  }

  // Get all scTimer data for sync
  getAllData() {
    const data = {
      times: JSON.parse(localStorage.getItem("scTimer-times") || "{}"),
      customSessions: JSON.parse(
        localStorage.getItem("scTimer-customSessions") || "[]"
      ),
      settings: {
        language: localStorage.getItem("scTimer-language"),
        decimalPlaces: localStorage.getItem("scTimer-decimalPlaces"),
        lastEvent: localStorage.getItem("scTimer-lastEvent"),
        lastEventText: localStorage.getItem("scTimer-lastEventText"),
        lastSessionId: localStorage.getItem("scTimer-lastSessionId"),
        // Add other settings as needed
      },
      timestamp: new Date().toISOString(),
      version: "1.0",
    };
    return data;
  }

  // Set all scTimer data from sync
  setAllData(data) {
    if (!data || typeof data !== "object") return false;

    try {
      // Restore times
      if (data.times) {
        localStorage.setItem("scTimer-times", JSON.stringify(data.times));
      }

      // Restore custom sessions
      if (data.customSessions) {
        localStorage.setItem(
          "scTimer-customSessions",
          JSON.stringify(data.customSessions)
        );
      }

      // Restore settings
      if (data.settings) {
        Object.entries(data.settings).forEach(([key, value]) => {
          if (value !== null && value !== undefined) {
            localStorage.setItem(`scTimer-${key}`, value);
          }
        });
      }

      return true;
    } catch (error) {
      console.error("Error setting sync data:", error);
      return false;
    }
  }

  // Upload data to Google Drive
  async uploadData() {
    if (!this.isSignedIn || this.syncInProgress) return false;

    this.syncInProgress = true;
    this.updateSyncStatus("Uploading...", "");

    try {
      const data = this.getAllData();
      const content = JSON.stringify(data, null, 2);

      // Check if file already exists
      const existingFile = await this.findSyncFile();

      let response;
      if (existingFile) {
        // Update existing file
        response = await gapi.client.request({
          path: `https://www.googleapis.com/upload/drive/v3/files/${existingFile.id}`,
          method: "PATCH",
          params: {
            uploadType: "media",
          },
          headers: {
            "Content-Type": "application/json",
          },
          body: content,
        });
      } else {
        // Create new file
        response = await gapi.client.request({
          path: "https://www.googleapis.com/upload/drive/v3/files",
          method: "POST",
          params: {
            uploadType: "multipart",
          },
          headers: {
            "Content-Type": 'multipart/related; boundary="foo_bar_baz"',
          },
          body: this.createMultipartBody(this.SYNC_FILE_NAME, content),
        });
      }

      if (response.status === 200) {
        this.updateSyncStatus("Upload successful", "connected");
        return true;
      } else {
        throw new Error(`Upload failed with status ${response.status}`);
      }
    } catch (error) {
      console.error("Upload failed:", error);
      this.updateSyncStatus("Upload failed", "error");
      return false;
    } finally {
      this.syncInProgress = false;
    }
  }

  // Download data from Google Drive
  async downloadData() {
    if (!this.isSignedIn || this.syncInProgress) return false;

    this.syncInProgress = true;
    this.updateSyncStatus("Downloading...", "");

    try {
      const file = await this.findSyncFile();
      if (!file) {
        this.updateSyncStatus("No sync file found", "error");
        return false;
      }

      const response = await gapi.client.drive.files.get({
        fileId: file.id,
        alt: "media",
      });

      if (response.status === 200) {
        const data = JSON.parse(response.body);

        // Show confirmation dialog before overwriting local data
        const translations = window.i18nModule?.translations || {};
        const syncTranslations = translations.sync || {};
        const confirmMessage =
          syncTranslations.downloadConfirm ||
          "This will replace your local data with data from Google Drive. Continue?";

        if (confirm(confirmMessage)) {
          if (this.setAllData(data)) {
            this.updateSyncStatus("Download successful", "connected");
            // Trigger page reload to reflect changes
            if (
              confirm(
                syncTranslations.reloadConfirm || "Reload page to see changes?"
              )
            ) {
              window.location.reload();
            }
            return true;
          } else {
            throw new Error("Failed to apply downloaded data");
          }
        } else {
          this.updateSyncStatus("Download cancelled", "");
          return false;
        }
      } else {
        throw new Error(`Download failed with status ${response.status}`);
      }
    } catch (error) {
      console.error("Download failed:", error);
      this.updateSyncStatus("Download failed", "error");
      return false;
    } finally {
      this.syncInProgress = false;
    }
  }

  // Find existing sync file
  async findSyncFile() {
    try {
      const response = await gapi.client.drive.files.list({
        q: `name='${this.SYNC_FILE_NAME}' and trashed=false`,
        spaces: "drive",
      });

      if (response.result.files && response.result.files.length > 0) {
        return response.result.files[0];
      }
      return null;
    } catch (error) {
      console.error("Error finding sync file:", error);
      return null;
    }
  }

  // Create multipart body for file upload
  createMultipartBody(filename, content) {
    const delimiter = "foo_bar_baz";
    const close_delim = `\r\n--${delimiter}--`;

    const metadata = {
      name: filename,
      parents: [], // Store in root folder
    };

    const multipartRequestBody =
      `--${delimiter}\r\n` +
      "Content-Type: application/json\r\n\r\n" +
      JSON.stringify(metadata) +
      "\r\n" +
      `--${delimiter}\r\n` +
      "Content-Type: application/json\r\n\r\n" +
      content +
      close_delim;

    return multipartRequestBody;
  }

  // Auto-sync when data changes (call this from main timer)
  async autoSync() {
    if (this.autoSyncEnabled && this.isSignedIn && !this.syncInProgress) {
      await this.uploadData();
    }
  }
}

// Create global instance
const syncManager = new SyncManager();

// Initialize when DOM is loaded
document.addEventListener("DOMContentLoaded", async () => {
  // Set up event listeners
  const connectBtn = document.getElementById("sync-connect-btn");
  const disconnectBtn = document.getElementById("sync-disconnect-btn");
  const uploadBtn = document.getElementById("sync-upload-btn");
  const downloadBtn = document.getElementById("sync-download-btn");
  const autoBtn = document.getElementById("sync-auto-btn");

  if (connectBtn) {
    connectBtn.addEventListener("click", () => syncManager.signIn());
  }

  if (disconnectBtn) {
    disconnectBtn.addEventListener("click", () => syncManager.signOut());
  }

  if (uploadBtn) {
    uploadBtn.addEventListener("click", () => syncManager.uploadData());
  }

  if (downloadBtn) {
    downloadBtn.addEventListener("click", () => syncManager.downloadData());
  }

  if (autoBtn) {
    autoBtn.addEventListener("click", () => syncManager.toggleAutoSync());
  }

  // Initialize the sync manager
  await syncManager.init();
});

// Make sync manager available globally
window.syncManager = syncManager;

export default syncManager;
