# Google Drive Sync Setup Guide

This guide will help you set up Google Drive sync for sc<PERSON><PERSON><PERSON> to sync your solve times across multiple devices.

## Prerequisites

- A Google account
- Access to Google Cloud Console

## Step 1: Create a Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Click "Select a project" → "New Project"
3. Enter project name: `scTimer-sync` (or any name you prefer)
4. Click "Create"

## Step 2: Enable Google Drive API

1. In the Google Cloud Console, go to "APIs & Services" → "Library"
2. Search for "Google Drive API"
3. Click on it and press "Enable"

## Step 3: Configure OAuth Consent Screen

1. Go to "APIs & Services" → "OAuth consent screen"
2. Choose "External" user type (unless you have a Google Workspace account)
3. Fill in the required information:
   - App name: `scTimer`
   - User support email: Your email
   - Developer contact information: Your email
4. Click "Save and Continue"
5. On the Scopes page, click "Save and Continue" (no additional scopes needed)
6. On the Test users page, add your email address if you want to test before publishing
7. Click "Save and Continue"

## Step 4: Create OAuth Credentials

1. Go to "APIs & Services" → "Credentials"
2. Click "Create Credentials" → "OAuth client ID"
3. Choose "Web application" as the application type
4. Name it: `scTimer Web Client`
5. Add authorized JavaScript origins:
   - `http://localhost` (for local testing)
   - Your domain if you're hosting scTimer online (e.g., `https://yourdomain.com`)
6. Click "Create"
7. Copy the "Client ID" from the popup (you'll need this)

## Step 5: Configure scTimer

You have two options to configure the Google Drive credentials:

### Option A: Using localStorage (Recommended for personal use)

1. Open scTimer in your browser
2. Open browser developer tools (F12)
3. Go to the Console tab
4. Run these commands (replace with your actual Client ID):

```javascript
localStorage.setItem('scTimer-gdrive-client-id', 'YOUR_CLIENT_ID_HERE');
```

### Option B: Using config file (Recommended for hosting)

1. Copy `config/gdrive-config.json.template` to `config/gdrive-config.json`
2. Edit the file and replace the placeholders with your actual credentials:

```json
{
  "clientId": "YOUR_ACTUAL_CLIENT_ID_HERE",
  "apiKey": ""
}
```

**Note:** The API key is optional for this implementation as we're using OAuth 2.0 flow.

## Step 6: Test the Setup

1. Open scTimer
2. Go to Settings (press 'S' or click the settings icon)
3. Scroll down to the "Google Drive Sync" section
4. Click "Connect"
5. You should see a Google sign-in popup
6. Grant permissions to access your Google Drive
7. Once connected, you should see "Connected as [your-email]"

## How to Use Google Drive Sync

### Manual Sync
- **Upload to Drive**: Saves your current solve times and settings to Google Drive
- **Download from Drive**: Replaces your local data with data from Google Drive
- **Auto Sync**: When enabled, automatically uploads data whenever you save a solve

### Auto Sync
- Toggle "Auto Sync" to automatically sync your data after each solve
- This ensures your data is always backed up to Google Drive
- Useful when using scTimer on multiple devices

## Security Notes

- Your solve times are stored in a private file in your Google Drive
- Only you can access this data
- The app only requests permission to access files it creates
- You can revoke access anytime from your Google Account settings

## Troubleshooting

### "Sign in failed" error
- Check that your Client ID is correctly configured
- Ensure your domain is added to authorized JavaScript origins
- Try clearing browser cache and cookies

### "No sync file found" when downloading
- This is normal if you haven't uploaded data yet
- Upload your data first, then you can download it on other devices

### Auto-sync not working
- Make sure you're signed in to Google Drive
- Check that Auto Sync is enabled (button should show "Auto Sync: On")
- Auto-sync only triggers when you save new solve times

## Privacy

- Your data is stored privately in your Google Drive
- No data is sent to any third-party servers
- The sync happens directly between your browser and Google Drive
- You maintain full control over your data

## Support

If you encounter issues with Google Drive sync, please check:
1. Browser console for error messages
2. Google Cloud Console for API quotas and errors
3. Your Google Account permissions for the scTimer app
