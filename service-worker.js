const CACHE_NAME = "sctimer-v4-" + new Date().getTime();

// Core app files - using relative paths for better compatibility
const APP_FILES = [
  "./",
  "./index.html",
  "./assets/css/styles.css",
  "./assets/css/fonts.css",
  "./assets/js/cubing-timer.js",
  "./assets/js/language-manager.js",
  "./assets/js/mbld-manager.js",
  "./assets/js/audio-manager.js",
  "./assets/js/lang/en.js",
  "./assets/js/lang/ar.js",
  "./assets/js/lang/ckb.js",
  "./manifest.json",
  "./assets/favicon.ico",
  "./assets/icons/72x72.png",
  "./assets/icons/96x96.png",
  "./assets/icons/128x128.png",
  "./assets/icons/144x144.png",
  "./assets/icons/152x152.png",
  "./assets/icons/192x192.png",
  "./assets/icons/384x384.png",
  "./assets/icons/512x512.png",
  "./assets/icons/1024x1024.png",
  "./assets/screenshots/mobile.png",
  "./assets/screenshots/desktop.png",
  "./assets/sounds/8sec.wav",
  "./assets/sounds/12sec.wav",
  "./assets/fonts/Rabar_21.ttf",
  "./assets/fonts/Rabar_22.ttf",
];

// External resources - we'll rely on fallbacks for all external resources
// since they have CORS issues and dynamic imports that are hard to cache
const EXTERNAL_RESOURCES = [];

// Combine all URLs to cache
const urlsToCache = [...APP_FILES, ...EXTERNAL_RESOURCES];

// Install event - cache assets with better error handling
self.addEventListener("install", (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME).then((cache) => {
      // Use a more resilient caching approach
      return Promise.all(
        urlsToCache.map((url) => {
          // Try to add each resource to the cache
          // Use no-cors mode for external resources to avoid CORS issues
          const options = url.startsWith("http")
            ? { mode: "no-cors" }
            : undefined;
          const request = new Request(url, options);

          return cache.add(request).catch(() => {
            // Continue even if one resource fails
            return Promise.resolve();
          });
        })
      );
    })
  );
});

// Activate event - clean up all caches
self.addEventListener("activate", (event) => {
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          return caches.delete(cacheName);
        })
      );
    })
  );
});

// Fetch event - serve from cache or network
self.addEventListener("fetch", (event) => {
  // Special handling for sound files
  if (event.request.url.includes("/assets/sounds/")) {
    event.respondWith(
      caches.open(CACHE_NAME).then((cache) => {
        return cache.match(event.request).then((response) => {
          if (response) {
            // If found in cache, return it
            return response;
          }

          // If not in cache, fetch from network
          return fetch(event.request)
            .then((networkResponse) => {
              // Only cache complete responses (status 200)
              if (networkResponse.status === 200) {
                // Clone the response to cache it
                const responseToCache = networkResponse.clone();
                cache.put(event.request, responseToCache).catch(() => {
                  // Silent error handling
                });
              }
              return networkResponse;
            })
            .catch((error) => {
              throw error;
            });
        });
      })
    );
    return;
  }

  // Standard handling for other resources
  event.respondWith(
    caches.match(event.request).then((response) => {
      // Cache hit - return response
      if (response) {
        return response;
      }

      // Clone the request
      const fetchRequest = event.request.clone();

      return fetch(fetchRequest)
        .then((response) => {
          // Check if valid response
          if (
            !response ||
            response.status !== 200 ||
            response.type !== "basic" ||
            event.request.url.startsWith("chrome-extension://")
          ) {
            return response;
          }

          // Clone the response
          const responseToCache = response.clone();

          caches.open(CACHE_NAME).then((cache) => {
            cache.put(event.request, responseToCache).catch(() => {
              // Silent error handling
            });
          });

          return response;
        })
        .catch(() => {
          // If fetch fails (offline), try to serve the offline page
          if (event.request.mode === "navigate") {
            return caches.match("/index.html");
          }
        });
    })
  );
});
